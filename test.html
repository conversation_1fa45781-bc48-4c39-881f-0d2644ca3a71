<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI智能体训练对话框测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            background: #f0f2f5;
        }

        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: all 0.3s ease;
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .description {
            background: #f8f9ff;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
            border-left: 4px solid #667eea;
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }

        .feature-list li:before {
            content: "✓";
            color: #4caf50;
            font-weight: bold;
            margin-right: 10px;
        }

        /* AI训练对话框样式 */
        .ai-training-dialog {
            display: none;
            position: fixed;
            z-index: 10001;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 1188px;
            height: 842px;
            max-width: 95vw;
            max-height: 95vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 16px;
            box-shadow: 0 12px 60px rgba(0, 0, 0, 0.25);
            flex-direction: column;
            resize: none;
            overflow: hidden;
            min-width: 600px;
            min-height: 842px;
            backdrop-filter: blur(20px);
            font-family: -apple-system, BlinkMacSystemFont, "Segue UI", Roboto, Helvetica, Arial, sans-serif;
            transition: all 0.3s ease;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .ai-training-dialog[style*="display: flex"] {
            display: flex !important;
        }

        .ai-training-dialog-header {
            padding: 20px 25px;
            cursor: move;
            z-index: 10;
            background: rgba(255, 255, 255, 0.15);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            border-top-left-radius: 16px;
            border-top-right-radius: 16px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 18px;
            font-weight: 700;
            color: white;
            text-shadow: 0 1px 2px rgba(0,0,0,0.1);
            backdrop-filter: blur(10px);
            flex-shrink: 0;
            min-height: 60px;
        }

        .header-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .header-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            font-size: 18px;
            cursor: pointer;
            color: white;
            line-height: 1;
            width: 32px;
            height: 32px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            font-family: Arial, sans-serif;
            font-weight: normal;
            padding: 0;
            margin: 0;
            text-align: center;
            vertical-align: middle;
        }

        .header-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: scale(1.1);
        }

        .search-bar {
            background: rgba(255, 255, 255, 0.1);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            padding: 15px 25px;
            backdrop-filter: blur(10px);
            animation: slideDown 0.3s ease;
        }

        .ai-training-dialog-body {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
            height: calc(100% - 80px);
            min-height: 742px;
        }

        .ai-training-messages {
            flex: 1;
            overflow-y: auto;
            padding: 30px;
            background: rgba(255, 255, 255, 0.95);
            position: relative;
            backdrop-filter: blur(20px);
            margin: 0;
            height: calc(100% - 120px);
            min-height: 540px;
            scrollbar-width: thin;
            scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
        }

        .ai-training-messages::-webkit-scrollbar {
            width: 8px;
        }

        .ai-training-messages::-webkit-scrollbar-track {
            background: transparent;
        }

        .ai-training-messages::-webkit-scrollbar-thumb {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 4px;
        }

        .ai-training-messages::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 0, 0, 0.3);
        }

        .ai-training-msg {
            display: flex;
            margin-bottom: 20px;
            max-width: 75%;
            animation: fadeInUp 0.3s ease;
            align-items: flex-end;
        }

        .ai-training-msg.user {
            margin-left: auto;
            flex-direction: row-reverse;
        }

        .ai-training-msg .avatar {
            width: 42px;
            height: 42px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            text-align: center;
            line-height: 42px;
            font-weight: 600;
            flex-shrink: 0;
            color: white;
            font-size: 12px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            margin-bottom: 5px;
        }

        .ai-training-msg.user .avatar {
            background: linear-gradient(135deg, #1E90FF 0%, #00BFFF 100%);
        }

        .ai-training-msg.bot .avatar {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }

        .ai-training-msg .content {
            margin: 0 15px;
            padding: 15px 20px;
            border-radius: 18px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            font-size: 14px;
            word-wrap: break-word;
            white-space: pre-wrap;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            position: relative;
            line-height: 1.5;
        }

        .ai-training-msg.user .content {
            background: linear-gradient(135deg, #1E90FF 0%, #00BFFF 100%);
            color: white;
            cursor: pointer;
        }

        .ai-training-input-area {
            padding: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            position: relative;
            flex-shrink: 0;
            min-height: 120px;
            box-sizing: border-box;
        }

        /* 问答训练按钮区域样式 */
        .qa-training-section {
            margin-bottom: 15px;
            display: flex;
            justify-content: center;
        }

        .qa-training-btn {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            border: none;
            border-radius: 20px;
            padding: 8px 16px;
            font-size: 13px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .qa-training-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 12px rgba(240, 147, 251, 0.4);
        }

        .qa-training-btn:active {
            transform: translateY(0);
        }

        .ai-training-input-container {
            position: relative;
            display: flex;
            align-items: flex-end;
            gap: 8px;
        }

        .upload-file-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 2px solid rgba(102, 126, 234, 0.3);
            background: rgba(102, 126, 234, 0.05);
            color: rgba(102, 126, 234, 0.8);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            font-size: 18px;
            font-weight: bold;
            backdrop-filter: blur(10px);
            flex-shrink: 0;
            margin-bottom: 5px;
        }

        .upload-file-btn:hover {
            background: rgba(102, 126, 234, 0.1);
            border-color: rgba(102, 126, 234, 0.6);
            transform: scale(1.05);
        }

        .ai-training-input-area textarea {
            flex-grow: 1;
            padding: 15px 60px 15px 20px;
            border: 2px solid rgba(102, 126, 234, 0.2);
            border-radius: 25px;
            resize: none;
            font-size: 14px;
            min-height: 50px;
            height: auto;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
            outline: none;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            line-height: 1.4;
            overflow-y: hidden;
            min-width: 0;
            max-height: 144px;
        }

        .ai-training-input-area textarea:focus {
            border-color: rgba(102, 126, 234, 0.6);
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
            background: rgba(255, 255, 255, 1);
        }

        .ai-training-input-area textarea::placeholder {
            color: rgba(0, 0, 0, 0.4);
        }

        #sendTrainingMsgBtn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: none;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            font-size: 16px;
            flex-shrink: 0;
            margin-bottom: 5px;
        }

        #sendTrainingMsgBtn:hover {
            transform: scale(1.1);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        #sendTrainingMsgBtn:active {
            transform: scale(0.95);
        }

        .file-input {
            display: none;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>AI智能体训练对话框优化测试</h1>
        
        <div class="description">
            <h3>本次优化功能：</h3>
            <ul class="feature-list">
                <li>在输入框上方添加"问答训练"按钮</li>
                <li>点击按钮自动插入问答模板：问题：\n\n回答：\n\n</li>
                <li>输入框根据内容行数自动调整高度（最大6行）</li>
                <li>输入框始终保持可见，行数多时自动向上移动</li>
                <li>优化用户体验，提高训练效率</li>
            </ul>
        </div>
        
        <button class="test-button" onclick="openAITrainingDialog()">
            打开AI智能体训练对话框
        </button>
        
        <div class="description">
            <h3>测试说明：</h3>
            <p>1. 点击上方按钮打开AI训练对话框</p>
            <p>2. 观察输入框上方是否有"问答训练"按钮</p>
            <p>3. 点击"问答训练"按钮，检查是否自动插入模板</p>
            <p>4. 在输入框中输入多行文本，观察高度是否自动调整</p>
            <p>5. 测试输入框是否始终保持可见</p>
        </div>
    </div>

    <!-- 引入主要的功能代码 -->
    <script src="功能代码.js"></script>
    
    <script>
        // 模拟打开AI训练对话框的函数
        function openAITrainingDialog() {
            // 首先确保页面已经加载了必要的HTML结构
            if (!document.getElementById('aiTrainingDialog')) {
                // 如果对话框不存在，先创建它
                const dialogHTML = `
                <div class="ai-training-dialog" id="aiTrainingDialog" style="display: none;">
                    <div class="ai-training-dialog-header" id="aiTrainingDialogHeader">
                        <span>AI 智能体训练</span>
                        <div class="header-controls">
                            <button id="searchToggleBtn" class="header-btn" title="搜索对话内容">🔍</button>
                            <button id="closeTrainingDialogBtn" class="header-btn">&times;</button>
                        </div>
                    </div>
                    <div class="search-bar" id="searchBar" style="display: none;">
                        <div class="search-container">
                            <input type="text" id="searchInput" placeholder="搜索对话内容..." />
                            <div class="search-controls">
                                <button id="searchPrevBtn" title="上一个">↑</button>
                                <button id="searchNextBtn" title="下一个">↓</button>
                                <span id="searchResults">0/0</span>
                                <button id="searchCloseBtn" title="关闭搜索">&times;</button>
                            </div>
                        </div>
                    </div>
                    <div class="ai-training-dialog-body">
                        <div class="ai-training-messages" id="aiTrainingMessages">
                            <div class="ai-training-msg bot">
                                <div class="avatar">AI</div>
                                <div class="content">欢迎使用AI智能体训练功能！请点击下方的"问答训练"按钮开始训练。</div>
                            </div>
                        </div>
                        <div class="ai-training-input-area">
                            <div class="qa-training-section">
                                <button id="qaTrainingBtn" class="qa-training-btn" title="快速插入问答训练模板">
                                    <span>📝</span>
                                    问答训练
                                </button>
                            </div>
                            <div class="ai-training-input-container">
                                <button id="uploadFileBtn" class="upload-file-btn" title="上传文件">+</button>
                                <textarea id="trainingInput" placeholder="在此输入内容或上传文件进行训练"></textarea>
                                <button id="sendTrainingMsgBtn">➤</button>
                            </div>
                            <input type="file" id="trainingFileInput" class="file-input" accept="image/*,video/*,.doc,.docx,.pdf,.xls,.xlsx,.txt" multiple style="display: none;">
                            <div id="uploadedFilesPreview" class="uploaded-files-preview"></div>
                        </div>
                    </div>
                </div>
                `;
                
                document.body.insertAdjacentHTML('beforeend', dialogHTML);
                
                // 初始化对话框功能
                if (typeof initTrainingDialog === 'function') {
                    initTrainingDialog();
                }
            }
            
            // 显示对话框
            const dialog = document.getElementById('aiTrainingDialog');
            if (dialog) {
                dialog.style.display = 'flex';
            }
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('测试页面已加载，可以开始测试AI训练对话框功能');
        });
    </script>
</body>
</html>
